{"name": "strapi-crm", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.22.0", "@strapi/plugin-users-permissions": "5.22.0", "@strapi/strapi": "5.22.0", "csv-parse": "^6.1.0", "pg": "8.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "strapi-plugin-tablify": "^1.0.3", "styled-components": "^6.0.0"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "fd942bec9d4d2a79fc028597bd2fa7c2efc65ebc04dbaeb3ee0cfa3926d7de00"}}