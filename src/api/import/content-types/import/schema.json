{"kind": "collectionType", "collectionName": "imports", "info": {"singularName": "import", "pluralName": "imports", "displayName": "Import"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"fileName": {"type": "string"}, "filePath": {"type": "string"}, "importStatus": {"type": "string"}, "total": {"type": "integer"}, "success": {"type": "integer"}, "error": {"type": "integer"}, "fieldMappings": {"type": "json"}, "assigned_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "imports"}}}