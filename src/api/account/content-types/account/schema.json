{"kind": "collectionType", "collectionName": "accounts", "info": {"singularName": "account", "pluralName": "accounts", "displayName": "Account"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "shortName": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "email"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "assigned_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "accounts"}, "contacts": {"type": "relation", "relation": "oneToMany", "target": "api::contact.contact", "mappedBy": "account"}}}