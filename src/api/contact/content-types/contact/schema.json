{"kind": "collectionType", "collectionName": "contacts", "info": {"singularName": "contact", "pluralName": "contacts", "displayName": "Contact"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "email"}, "phone": {"type": "string"}, "account": {"type": "relation", "relation": "manyToOne", "target": "api::account.account", "inversedBy": "contacts"}, "assigned_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "contacts"}}}