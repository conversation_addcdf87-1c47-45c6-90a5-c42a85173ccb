import React from 'react';
// @ts-ignore
import pluginId from './pluginId';

export default {
  register(app) {
    app.addMenuLink({
      to: `/plugins/${pluginId}`,
      icon: () => <span>⚙️</span>,
      intlLabel: {
        id: `${pluginId}.plugin.name`,
        defaultMessage: 'UI Settings',
      },
      Component: async () => {
        // @ts-ignore
        const component = await import('./pages/Settings');
        return component.default;
      },
      permissions: [],
    });

    app.registerPlugin({
      id: pluginId,
      name: 'UI Settings',
    });
  },

  bootstrap() {},
};
